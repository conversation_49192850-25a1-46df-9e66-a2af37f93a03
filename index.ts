

import { config } from "https://deno.land/x/dotenv@v3.2.2/mod.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { corsHeaders } from "./cors.ts";
import nodemailer from "npm:nodemailer@6.9.10";


config(); 

const transport = nodemailer.createTransport({
  host: Deno.env.get("SMTP_HOST"),
  port: Number(Deno.env.get("SMTP_PORT")),
  auth: {
    user: Deno.env.get("SMTP_USER"),
    pass: Deno.env.get("SMTP_PASS"),
  },
});

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }
  // Check if the request method is POST
  if (req.method === "POST") {
    const data = await req.json();

    const recipientEmail = data.email;
    const mailSubject = data.subject;
    const mailContent = data.mail_content;

    // Send email using Deno SMTP
    try {
      await new Promise<void>((resolve, reject) => {
        transport.sendMail({
          from: Deno.env.get("LOCAL_ADDRESS_FROM"),
          to: recipientEmail,
          subject: mailSubject,
          text: mailContent,
        }, (error: any) => {
          if (error) {
            return reject(error);
          }

          resolve();
        });
      });
    } catch (error) {
      console.error("Error sending email:", error);
      return new Response(
        JSON.stringify({ status: "error", message: "Failed to send email" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    return new Response(
      JSON.stringify({ status: "success", message: "Email sent" }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  } else {
    return new Response("Method not allowed", { status: 405 });
  }
});